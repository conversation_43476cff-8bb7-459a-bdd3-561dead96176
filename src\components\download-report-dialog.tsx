"use client";

import { ReportType } from "@prisma/client";
import { useRouter } from "next/navigation";
import { useEffect, useState, useRef } from "react";
import { fetchReportTemplateListByType } from "@/src/actions/report-template";
import { toast } from "../hooks/use-toast";
import { But<PERSON> } from "./ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "./ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { AlertCircle, Clock, FileText } from "lucide-react";

type SelectItemType = {
  label: string;
  value: string;
};

interface DownloadReportDialogProps {
  reportType: ReportType;
  openUploadDialog: boolean;
  additionalParamsOptional?: any;
  setOpenUploadDialog: (value: any) => void;
  reportMergeAction: (
    templateKey: string,
    additionalParams?: any
  ) => Promise<{ fileEditorId: string } | undefined>;
}

export default function DownloadReportDialog({
  reportType,
  reportMergeAction,
  openUploadDialog,
  setOpenUploadDialog,
  additionalParamsOptional,
}: DownloadReportDialogProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [reportTemplateKey, setReportTemplateKey] = useState<
    string | undefined
  >();
  const [reportTemplateList, setReportTemplateList] = useState<
    SelectItemType[]
  >([]);
  const [progress, setProgress] = useState(0);
  const [generationStep, setGenerationStep] = useState("");
  const [showTimeoutWarning, setShowTimeoutWarning] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const progressRef = useRef<NodeJS.Timeout>();
  const router = useRouter();

  const fetchReportTemplateList = async () => {
    const templateList = await fetchReportTemplateListByType(reportType);
    if (templateList) {
      setReportTemplateList(
        templateList.map((t) => {
          return {
            value: t.id,
            label: t.title,
          };
        })
      );
    }
  };

  useEffect(() => {
    if (openUploadDialog) {
      fetchReportTemplateList();
    }
  }, [openUploadDialog]);

  // Função para simular progresso durante a geração
  const simulateProgress = () => {
    setProgress(0);
    setGenerationStep("Carregando template...");

    let currentProgress = 0;
    const steps = [
      { progress: 20, step: "Processando dados..." },
      { progress: 40, step: "Substituindo variáveis..." },
      { progress: 60, step: "Processando imagens..." },
      { progress: 80, step: "Finalizando documento..." },
      { progress: 95, step: "Quase pronto..." }
    ];

    let stepIndex = 0;
    progressRef.current = setInterval(() => {
      if (stepIndex < steps.length) {
        setProgress(steps[stepIndex].progress);
        setGenerationStep(steps[stepIndex].step);
        stepIndex++;
      }
    }, 3000); // Atualiza a cada 3 segundos
  };

  // Limpar timeouts quando o componente for desmontado ou diálogo fechado
  useEffect(() => {
    if (!openUploadDialog) {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
      if (progressRef.current) clearInterval(progressRef.current);
      setProgress(0);
      setGenerationStep("");
      setShowTimeoutWarning(false);
      setIsGenerating(false);
    }
  }, [openUploadDialog]);

  const handleReportMerge = async () => {
    if (reportTemplateKey) {
      try {
        setIsGenerating(true);
        setShowTimeoutWarning(false);

        // Iniciar simulação de progresso
        simulateProgress();

        // Configurar timeout de aviso após 30 segundos
        timeoutRef.current = setTimeout(() => {
          setShowTimeoutWarning(true);
          setGenerationStep("Processando template extenso... Isso pode levar alguns minutos.");
        }, 30000);

        const response = await reportMergeAction(
          reportTemplateKey,
          additionalParamsOptional
        );

        // Limpar timeouts
        if (timeoutRef.current) clearTimeout(timeoutRef.current);
        if (progressRef.current) clearInterval(progressRef.current);

        if (response) {
          setProgress(100);
          setGenerationStep("Redirecionando...");

          // Pequeno delay para mostrar o progresso completo
          setTimeout(() => {
            router.push(`/document-editor/${response.fileEditorId}`);
            setIsGenerating(false);
          }, 500);
        }
      } catch (error) {
        console.error(error);

        // Limpar timeouts
        if (timeoutRef.current) clearTimeout(timeoutRef.current);
        if (progressRef.current) clearInterval(progressRef.current);

        // Verificar se é erro de timeout
        const errorMessage = error instanceof Error ? error.message : "Falha ao gerar relatório.";
        const isTimeoutError = errorMessage.toLowerCase().includes('timeout') ||
          errorMessage.toLowerCase().includes('time') ||
          errorMessage.toLowerCase().includes('exceeded');

        toast({
          title: "Erro",
          description: isTimeoutError
            ? "O template é muito extenso e excedeu o tempo limite. Tente usar um template menor ou entre em contato com o suporte."
            : errorMessage,
          variant: "destructive",
        });
        setIsGenerating(false);
        setProgress(0);
        setGenerationStep("");
        setShowTimeoutWarning(false);
      }
    }
  };

  return (
    <Dialog
      open={openUploadDialog}
      onOpenChange={(open) => setOpenUploadDialog(open)}
    >
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="font-bold text-xl flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Gerar relatório pelo modelo
          </DialogTitle>
          <DialogDescription>
            Selecione o modelo de template para gerar o relatório
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col space-y-4">
          {reportTemplateList && !isGenerating && (
            <Select
              onValueChange={(value) => setReportTemplateKey(value)}
              value={reportTemplateKey}
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione o modelo" />
              </SelectTrigger>
              <SelectContent>
                {reportTemplateList?.map(({ value, label }, index) => (
                  <SelectItem value={value} key={index}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}

          {isGenerating && (
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Clock className="h-4 w-4 animate-spin" />
                {generationStep}
              </div>

              {/* Barra de progresso customizada sem dependências */}
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
              <div className="text-xs text-center text-muted-foreground">
                {progress}% concluído
              </div>

              {showTimeoutWarning && (
                <div className="flex items-start gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-yellow-800">
                    O template selecionado é extenso e pode levar alguns minutos para processar.
                    Por favor, aguarde...
                  </div>
                </div>
              )}
            </div>
          )}

          <Button
            onClick={handleReportMerge}
            disabled={isGenerating || !reportTemplateKey}
            className="w-full"
          >
            {isGenerating ? (
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 animate-spin" />
                Gerando relatório...
              </div>
            ) : (
              "Gerar relatório"
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
