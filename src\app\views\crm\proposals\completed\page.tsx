"use client"
import ContentWrapper from "@/src/components/content-wrapper";
import { Column } from "@/src/components/ui/table-grid";
import { proposalSituations, serviceTypeOptions } from "@/src/constants";
import { formatCurrency, formatDate } from "@/src/lib/utils";
import { Customer } from "@/src/types/core/customer";
import { Proposal, ProposalSituation } from "@/src/types/core/proposal";
import { Eye } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import ContractsCompletedTable, { ContractsCompletedTableRef } from "./_components/contracts-completed-table";

export default function CompletedProposals() {
    const router = useRouter();
    const [loading, setLoading] = useState(true);
    const [isNavigating, setIsNavigating] = useState(false);
    const [customers, setCustomers] = useState<Customer[]>([]);
    // const [currentPage, setCurrentPage] = useState(1);
    const tableRef = useRef<ContractsCompletedTableRef>(null);


    const fetchCustomers = async () => {
        try {
            const res = await fetch("/api/customers?pageSize=1000");
            if (!res.ok) throw new Error("Erro ao carregar clientes");

            const result = await res.json();
            console.log("Dados de clientes recebidos:", result);

            // Verificar se os dados estão na propriedade 'data' ou diretamente no resultado
            const customers = result.data || result.items || result;

            // Garantir que customers é um array
            if (!Array.isArray(customers)) {
                console.error("Dados de clientes não são um array:", customers);
                return [];
            }

            return customers as Customer[];
        } catch (error) {
            console.error("Erro ao carregar clientes:", error);
            return [];
        }
    };

    async function loadData() {
        setLoading(true);
        try {
            const customersData = await fetchCustomers();
            setCustomers(customersData || []);
        } catch (error) {
            console.error('Erro ao carregar dados:', error);
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        loadData();
    }, []);


    const handleNavigation = (url: string) => {
        setIsNavigating(true);
        router.push(url);
    };

    const columns: Column<Proposal>[] = [
        {
            key: "customer",
            header: "Cliente",
            cell: (row) => (row.customer as Customer).name,
            sortable: true,
            filterable: true,
            filterOptions: Array.isArray(customers) ? customers.map((c) => ({
                label: c.name,
                value: c.id,
            })) : [],
            getFilterValue: (row) => (row.customer as Customer).id,
        },
        {
            key: "name",
            header: "Projeto",
            sortable: true,
        },
        {
            key: "budget",
            header: "Orçamento",
            cell: (row) => formatCurrency(row.budget),
            sortable: true,
        },
        {
            key: "startDate",
            header: "Data início",
            cell: (row) => formatDate(row.startDate, "DATE"),
            sortable: true,
        },
        {
            key: "endDate",
            header: "Data de conclusão",
            cell: (row) => formatDate(row.endDate, "DATE"),
            sortable: true,
        },
        {
            key: "serviceType",
            header: "Tipo de Serviço",
            cell: (row) => {
                const serviceTypeLabel = serviceTypeOptions.find(
                    (option) => option.value === row.serviceType
                )?.label || row.serviceType || '';

                return (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-600">
                        {serviceTypeLabel}
                    </span>
                );
            },
            sortable: true,
        },
        {
            key: "situation",
            header: "Situação",
            cell: (row) => {
                const rowValue = row.situation as ProposalSituation;
                const situationLabel = proposalSituations.find(
                    (situation) => situation.value == rowValue
                )?.label || '';

                return (
                    <span className="status-badge status-badge-finished">
                        {situationLabel}
                    </span>
                );
            },
            sortable: true,
        },
        {
            key: "actions",
            header: "Ações",
            cell: (row) => (
                <div className="flex gap-3">
                    <Eye
                        className="size-5 text-green-500 cursor-pointer"
                        onClick={() =>
                            handleNavigation(`/views/crm/proposals/completed/${row.id}?from=completed`)
                        }
                    />
                </div>
            ),
        },
    ];

    return (
        <ContentWrapper
            title="Contratos concluídos"
            loading={loading || isNavigating}
        >
            <ContractsCompletedTable
                ref={tableRef}
                columns={columns}
            // onPageChange={(page) => setCurrentPage(page)}
            />
        </ContentWrapper>
    );
}
