"use server";
import { MINIO_ENDPOINT, MINIO_BUCKET_NAME } from "@/src/lib/env/variables";
import { prisma } from "@/src/lib/prisma";
import { ProposalFilters } from "@/src/types/core/proposal";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";
import { formatDate } from "@/src/lib/utils";
import { toKebabCase } from "@/src/lib/utils";

export async function getProposalsWithInspectionParameters(
  filters?: ProposalFilters
) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const query: any = {
      where: {
        customer: {
          organizationId,
        },
        inspectionParameters: {
          some: {},
        },
        situation: {
          in: [
            "PROPOSAL_ACCEPTED",
            "SIGN_REQUESTED",
            "SIGNED",
            "PROJECT_IN_PROGRESS",
            "PROJECT_FINISHED",
          ],
        },
        ...(filters?.customerId && { customerId: filters.customerId }),
      },
      include: {
        customer: true,
        inspectionParameters: {
          include: {
            photos: true,
          },
        },
      },
    };

    const data = await prisma.proposal.findMany(query);

    return data;
  } catch (err) {
    console.error(err);
  }
}

async function getPluviosityForInspection(city: string, inspectionDate: Date) {
  const dateKey = inspectionDate.toISOString().split("T")[0];
  const pluviosity = await prisma.pluviosity.findFirst({
    where: {
      dateKey,
      city: toKebabCase(city),
    },
    select: {
      value: true,
    },
  });

  return pluviosity?.value?.toString() ?? "0";
}

function getWeatherCondition(pluviosityValue: string): string {
  const value = parseFloat(pluviosityValue);

  if (value === 0) return "Dia claro, sem precipitação";
  if (value <= 0.2) return "Garoa leve";
  if (value <= 2.5) return "Chuva fraca";
  if (value <= 7.6) return "Chuva moderada";
  if (value <= 50) return "Chuva forte";
  return "Chuva muito forte";
}

function generateGoogleMapsLink(
  address: string,
  city: string,
  state: string,
  cep?: string
): string {
  // Monta o endereço completo para busca no Google Maps
  const addressParts = [address, city, state, cep].filter(
    (part) => part && part.trim() !== ""
  );
  const fullAddress = addressParts.join(", ");

  // Codifica o endereço para URL
  const encodedAddress = encodeURIComponent(fullAddress);

  // Retorna o link do Google Maps
  return `https://www.google.com/maps/search/?api=1&query=${encodedAddress}`;
}

export async function generateReport(
  proposalId: string,
  inspectionId?: string
) {
  try {
    const prompt =
      "Você é um engenheiro civil especialista e atual desenvolvendo laudos técnicos de construções e reformas. O laudo técnico deve conter informações detalhadas baseada nas seguintes vistorias realizadas: (te informarei data da vistoria, dados técnicos coletados e observações feitas). \n";
    const inspectionParameters = await prisma.inspectionParameter.findMany({
      where: {
        proposalId: proposalId,
        ...(inspectionId && { id: inspectionId }),
      },
    });
    let pluviosityValue = "Sem dados";

    const data = await prisma.proposal.findUnique({
      where: {
        id: proposalId,
      },
      include: {
        customer: true,
        inspectionParameters: {
          where: inspectionId ? { id: inspectionId } : undefined,
          include: {
            photos: {
              include: {
                file: true,
              },
            },
          },
        },
      },
    });

    if (!data) {
      throw new Error("Proposal not found");
    }

    const formattedContext = inspectionParameters
      .map((item) => {
        const date = item.inspectionDate.toISOString().split("T")[0];
        return `${date} - ${item.technicalData}. Observação: ${item.observation}`;
      })
      .join("\n");

    const context = prompt.concat(formattedContext);

    const suggestions = await generateSuggestionsOpenAI(context);
    const reportText = await generateReportText(context);

    if (inspectionId) {
      pluviosityValue = inspectionId
        ? await getPluviosityForInspection(
            data.city ?? "",
            data.inspectionParameters[0]?.inspectionDate
          )
        : "Sem dados";
    }

    const variables: any = {
      obra: data.name,
      nome: data.customer.name,
      cnpj: data.customer.document,
      cliente: data.customer.name,
      endereco: data.address ?? "",
      cidade: data.city ?? "",
      estado: data.state ?? "",
      cep: data.cep,
      linkGoogleMaps: generateGoogleMapsLink(
        data.address ?? "",
        data.city ?? "",
        data.state ?? "",
        data.cep ?? ""
      ),
      relatorio: reportText,
      sugestoes: suggestions,
      comentarios: inspectionParameters
        .map((param) => param.observation)
        .filter((observation) => observation && observation.trim() !== "")
        .join("\n\n"),
      dadosTecnicos: inspectionParameters
        .map((param) => param.technicalData)
        .filter((technicalData) => technicalData && technicalData.trim() !== "")
        .join("\n\n"),
      dataHoje: formatDate(new Date(), "DATE"),
      dia: new Date(data.startDate).getDate(),
      mes: new Date(data.startDate).toLocaleString("pt-BR", { month: "long" }),
      ano: new Date(data.startDate).getFullYear(),
      diaFinal: new Date(data.endDate).getDate(),
      mesFinal: new Date(data.endDate).toLocaleString("pt-BR", {
        month: "long",
      }),
      anoFinal: new Date(data.endDate).getFullYear(),
      prazoContratual: `${Math.ceil(
        (new Date(data.endDate).getTime() -
          new Date(data.startDate).getTime()) /
          (1000 * 60 * 60 * 24)
      )} dias`,
      prazoDecorrido: `${Math.ceil(
        (new Date().getTime() - new Date(data.startDate).getTime()) /
          (1000 * 60 * 60 * 24)
      )} dias`,
      prazoVencer: `${Math.ceil(
        (new Date(data.endDate).getTime() - new Date().getTime()) /
          (1000 * 60 * 60 * 24)
      )} dias`,
      items: data.inspectionParameters.flatMap((param, index) =>
        param.photos.map((photo) => ({
          image: `${MINIO_ENDPOINT}/${MINIO_BUCKET_NAME}/${photo?.file?.path}`,
          numero: index + 1,
          legenda: photo.description,
        }))
      ),
      itemsPares: (() => {
        const allItems = data.inspectionParameters.flatMap((param, index) =>
          param.photos.map((photo) => ({
            image: `${MINIO_ENDPOINT}/${MINIO_BUCKET_NAME}/${photo?.file?.path}`,
            numero: index + 1,
            legenda: photo.description,
          }))
        );

        const pares: any[] = [];
        for (let i = 0; i < allItems.length; i += 2) {
          pares.push({
            imagem1: allItems[i] || null,
            imagem2: allItems[i + 1] || null,
          });
        }
        return pares;
      })(),
      primeiraImagem: (() => {
        const allItems = data.inspectionParameters.flatMap((param, index) =>
          param.photos.map((photo) => ({
            image: `${MINIO_ENDPOINT}/${MINIO_BUCKET_NAME}/${photo?.file?.path}`,
            numero: index + 1,
            legenda: photo.description,
          }))
        );
        // Retornar apenas a URL da primeira imagem para compatibilidade com {{primeiraImagem}}
        // Se você quiser usar {{#primeiraImagem}}{{%image}}{{/primeiraImagem}}, use primeiraImagemObj
        return allItems.length > 0
          ? `${MINIO_ENDPOINT}/${MINIO_BUCKET_NAME}/${data.inspectionParameters[0]?.photos[0]?.file?.path}`
          : "";
      })(),
      primeiraImagemObj: (() => {
        const allItems = data.inspectionParameters.flatMap((param, index) =>
          param.photos.map((photo) => ({
            image: `${MINIO_ENDPOINT}/${MINIO_BUCKET_NAME}/${photo?.file?.path}`,
            numero: index + 1,
            legenda: photo.description,
          }))
        );
        // Retornar o objeto completo para uso em loops (se necessário)
        return allItems.length > 0 ? allItems[0] : null;
      })(),
      ...(inspectionId && {
        dataInspecao: formatDate(
          data.inspectionParameters[0]?.inspectionDate,
          "DATE"
        ),
        pluviometrico: pluviosityValue,
        condicaoClimatica: getWeatherCondition(pluviosityValue),
      }),
    };

    return variables;
  } catch (err) {
    console.error(err);
  }
}

async function generateSuggestionsOpenAI(context: string): Promise<string> {
  const prompt = `Com base no seguinte contexto: "${context}", sugira uma série de intervenções que podem ser feitas para resolver os problemas encontrados na vistoria. Faça um breve parágrafo resumindo as ações e, em seguida, crie uma sequência de bullets descrevendo cada intervenção detalhadamente. Finalize com uma conclusão.`;

  const response = await fetch("https://api.openai.com/v1/completions", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
    },
    body: JSON.stringify({
      model: "gpt-3.5-turbo-instruct",
      prompt,
      max_tokens: 600,
      temperature: 0.7,
    }),
  });

  if (!response.ok) {
    throw new Error(`Error ${response.status}: ${response.statusText}`);
  }

  const data = await response.json();
  let texto = data.choices[0].text.trim();
  texto = texto
    .replace(/(\n)\n/g, "$1")
    .replace(/(?<!\n)\n(?!\n)/g, " ")
    .replace(/^(?!\s*$)/gm, "\t")
    .replace(/(?:\s*-\s+)/g, "\n- ");

  return texto;
}

async function generateReportText(context: string): Promise<string> {
  const prompt = `Com base no seguinte contexto: "${context}": \n Gere um texto com vários parágrafos descrevendo cronologicamnente o que foi coletado com riqueza de detalhes utilizando termos técnicos da área de engenharia civil."`;

  console.log(`Bearer ${process.env.OPENAI_API_KEY}`);
  console.log(prompt);

  const response = await fetch("https://api.openai.com/v1/completions", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
    },
    body: JSON.stringify({
      model: "gpt-3.5-turbo-instruct",
      prompt,
      max_tokens: 1000,
      temperature: 0.7,
    }),
  });

  if (!response.ok) {
    throw new Error(`Error ${response.status}: ${response.statusText}`);
  }

  const data = await response.json();
  let texto = data.choices[0].text.trim();
  texto = texto
    .replace(/(?<!\n)\n(?!\n)/g, " ")
    .replace(/(\n)\n/g, "$1")
    .replace(/^(?!\s*$)/gm, "\t");

  return texto;
}
