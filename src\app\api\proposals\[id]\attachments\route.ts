import { NextRequest, NextResponse } from "next/server";
import { addProposalAttachments, removeProposalAttachment, getProposalAttachments } from "@/src/actions/proposals";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const proposalId = params.id;
    const attachments = await getProposalAttachments(proposalId);
    return NextResponse.json(attachments);
  } catch (error) {
    console.error("Erro ao buscar anexos:", error);
    return NextResponse.json(
      { error: "Erro ao buscar anexos" },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const proposalId = params.id;
    const formData = await request.formData();
    
    const files = formData.getAll("files") as File[];
    
    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: "Nenhum arquivo foi enviado" },
        { status: 400 }
      );
    }

    // Converter File para o formato esperado pela action
    const fileData = await Promise.all(
      files.map(async (file) => ({
        name: file.name,
        type: file.type,
        size: file.size,
        buffer: Buffer.from(await file.arrayBuffer()),
      }))
    );

    const savedFiles = await addProposalAttachments(proposalId, fileData as any);
    return NextResponse.json(savedFiles);
  } catch (error) {
    console.error("Erro ao adicionar anexos:", error);
    return NextResponse.json(
      { error: "Erro ao adicionar anexos" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const proposalId = params.id;
    const { searchParams } = new URL(request.url);
    const fileId = searchParams.get("fileId");

    if (!fileId) {
      return NextResponse.json(
        { error: "ID do arquivo é obrigatório" },
        { status: 400 }
      );
    }

    const result = await removeProposalAttachment(proposalId, fileId);
    return NextResponse.json(result);
  } catch (error) {
    console.error("Erro ao remover anexo:", error);
    return NextResponse.json(
      { error: "Erro ao remover anexo" },
      { status: 500 }
    );
  }
}
