"use client";

import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from "react";
import { TableGrid } from "@/src/components/ui/table-grid";
import { Proposal } from "@/src/types/core/proposal";
import { useToast } from "@/src/hooks/use-toast";
import { Button } from "@/src/components/ui/button";
import { Eraser, Plus } from "lucide-react";

interface ProposalsTableProps {
  columns: any[];
  onAddClick: () => void;
  onPageChange?: (page: number) => void;
  situations: string[];
  serviceTypes?: string[];
}

export type ProposalsTableRef = {
  refresh: (page?: number, searchTerm?: string, situationValue?: string | null) => void;
  refreshWithApiUrl: (apiUrl: string) => void;
  getTableState: () => {
    page: number;
    pageSize: number;
    search: string;
    situationFilter: string | null;
  };
};

const ProposalsTable = forwardRef<ProposalsTableRef, ProposalsTableProps>(
  function ProposalsTable({ columns, onAddClick, onPageChange, situations, serviceTypes }, ref) {
    const [data, setData] = useState<Proposal[]>([]);
    const [loading, setLoading] = useState(false);
    const [search, setSearch] = useState("");
    const [situationFilter, setSituationFilter] = useState<string | null>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);
    const { toast } = useToast();
    const [pagination, setPagination] = useState({
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });

    // Estado para rastrear filtros ativos
    const [activeFilters, setActiveFilters] = useState<{ [key: string]: boolean }>({});

    const fetchProposals = useCallback(async (
      page?: number,
      pageSize?: number,
      searchTerm: string = search,
      situationValue: string | null = situationFilter
    ) => {
      // Usar a página fornecida ou a página atual da paginação, ou 1 como último recurso
      const currentPage = page !== undefined ? page : (pagination.page || 1);

      // Usar o tamanho da página fornecido ou o tamanho da página atual da paginação
      const currentPageSize = pageSize !== undefined ? pageSize : pagination.pageSize;

      console.log("fetchProposals chamado com:", {
        page,
        pageSize,
        searchTerm,
        situationValue,
        currentPage,
        currentPageSize,
        paginationPage: pagination.page,
        paginationPageSize: pagination.pageSize
      });

      try {
        setLoading(true);
        const params = new URLSearchParams({
          page: String(currentPage),
          pageSize: String(currentPageSize)
        });

        // Adicionar situações base (sempre incluir as situações permitidas)
        if (situationValue) {
          // Se houver um filtro de situação específico, usar apenas ele
          params.append('situation', situationValue);
        } else {
          // Caso contrário, usar todas as situações permitidas
          situations.forEach(situation => {
            params.append('situation', situation);
          });
        }

        // Adicionar termo de busca se fornecido
        if (searchTerm) {
          params.append('search', searchTerm);
        }

        // Adicionar tipos de serviço se fornecidos
        if (serviceTypes && serviceTypes.length > 0) {
          serviceTypes.forEach(serviceType => {
            params.append('serviceType', serviceType);
          });
        }

        // Construir a URL da API
        const apiUrl = `/api/proposals?${params}`;

        // Salvar a URL da API no localStorage para uso posterior (ex: exclusão)
        if (typeof window !== 'undefined') {
          localStorage.setItem('lastProposalApiUrl', apiUrl);
          console.log("URL da API salva no localStorage:", apiUrl);
        }

        const response = await fetch(apiUrl);

        if (!response.ok) throw new Error("Failed to fetch proposals");

        const result = await response.json();

        setData(Array.isArray(result.data) ? result.data : []);
        setPagination(prev => ({
          ...prev,
          page: Number(result.page),
          pageSize: Number(result.pageSize),
          total: Number(result.total),
          totalPages: Number(result.totalPages)
        }));

      } catch (error) {
        console.error('Fetch error:', error);
        toast({
          title: "Erro",
          description: "Erro ao carregar propostas",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }, [pagination.pageSize, search, situationFilter, situations, serviceTypes, toast]);

    useImperativeHandle(ref, () => ({
      refresh: (page?: number, searchTerm?: string, situationValue?: string | null) => {
        // Se os parâmetros forem fornecidos, use-os; caso contrário, use os valores atuais
        const newSearch = searchTerm !== undefined ? searchTerm : search;
        const newSituationFilter = situationValue !== undefined ? situationValue : situationFilter;

        // Usar a página fornecida, ou a página atual da paginação, ou 1 como último recurso
        // Importante: não redefinir para 1 automaticamente
        const currentPage = page !== undefined ? page : (pagination.page || 1);

        console.log("ProposalsTable.refresh chamado com:", {
          page,
          currentPage,
          paginationPage: pagination.page,
          newSearch,
          newSituationFilter
        });

        // Atualizar os estados locais se os valores forem diferentes
        if (newSearch !== search) {
          setSearch(newSearch);
        }

        if (newSituationFilter !== situationFilter) {
          setSituationFilter(newSituationFilter);
          setActiveFilters(prev => ({
            ...prev,
            situation: !!newSituationFilter
          }));
        }

        // Definir explicitamente a página atual para garantir que seja mantida
        if (page !== undefined) {
          console.log("Definindo página explicitamente para:", page);
        }

        // Não remover a URL da API do localStorage para permitir que o componente seja remontado com a mesma página
        // if (typeof window !== 'undefined') {
        //   localStorage.removeItem('lastProposalApiUrl');
        // }

        // Buscar propostas com os novos valores
        fetchProposals(page, pagination.pageSize, newSearch, newSituationFilter);
      },
      refreshWithApiUrl: (apiUrl: string) => {
        console.log("ProposalsTable.refreshWithApiUrl chamado com URL:", apiUrl);

        try {
          // Extrair parâmetros da URL da API
          const fullUrl = apiUrl.startsWith('http') ? apiUrl : window.location.origin + apiUrl;
          const url = new URL(fullUrl);

          // Extrair a página da URL da API
          const page = url.searchParams.get('page');
          const pageNum = page ? parseInt(page, 10) : 1;

          // Extrair o termo de busca da URL da API
          const searchTerm = url.searchParams.get('search') || '';

          // Extrair os filtros de situação da URL da API
          const situations = url.searchParams.getAll('situation');
          const situationFilter = situations.length > 0 ? situations.join(',') : null;

          console.log("Parâmetros extraídos da URL da API:", { pageNum, searchTerm, situationFilter });

          // Atualizar os estados locais
          setSearch(searchTerm);
          setSituationFilter(situationFilter);
          setActiveFilters(prev => ({
            ...prev,
            situation: !!situationFilter
          }));

          // Buscar propostas com os parâmetros extraídos
          fetchProposals(pageNum, pagination.pageSize, searchTerm, situationFilter);
        } catch (error) {
          console.error("Erro ao extrair parâmetros da URL da API:", error);
          // Fallback: buscar propostas com os valores atuais
          fetchProposals(pagination.page, pagination.pageSize, search, situationFilter);
        }
      },
      getTableState: () => ({
        page: pagination.page,
        pageSize: pagination.pageSize,
        search,
        situationFilter
      })
    }), [fetchProposals, pagination.page, pagination.pageSize, search, situationFilter]);

    // Removido o evento refreshProposals, pois agora estamos usando apenas o método refresh do componente TableGrid

    // Carregar dados apenas uma vez quando o componente for montado
    useEffect(() => {
      // Verificar se há uma URL da API salva no localStorage e se estamos em um processo de exclusão
      if (typeof window !== 'undefined') {
        const savedApiUrl = localStorage.getItem('lastProposalApiUrl');
        const isDeleting = localStorage.getItem('isProposalDeleting');

        console.log("Verificando flag de exclusão:", { isDeleting, savedApiUrl });

        // Verificar se estamos em um processo de exclusão
        const isInDeletionProcess = isDeleting === 'true';

        // Se não estamos em um processo de exclusão, carregar a página 1 e apagar a variável lastProposalApiUrl
        if (!isInDeletionProcess) {
          console.log("Não estamos em um processo de exclusão, carregando página 1 e apagando a variável lastProposalApiUrl");

          // Limpar a URL da API imediatamente
          if (savedApiUrl) {
            localStorage.removeItem('lastProposalApiUrl');
            console.log("URL da API removida do localStorage no carregamento inicial");
          }

          // Carregar dados iniciais com valores padrão
          const initialPage = 1;
          const initialSearch = '';
          const initialSituationFilter: string | null = null;

          console.log("Carregamento inicial com valores padrão");

          // Carregar dados iniciais
          fetchProposals(initialPage, pagination.pageSize, initialSearch, initialSituationFilter);
          return;
        }

        // Se estamos em um processo de exclusão e há uma URL da API salva, usar a URL da API
        if (isInDeletionProcess && savedApiUrl) {
          console.log("Carregamento inicial com URL da API salva no localStorage (processo de exclusão):", savedApiUrl);

          // Limpar a flag de exclusão após o carregamento inicial
          // Isso evita que a flag permaneça definida indefinidamente
          setTimeout(() => {
            localStorage.removeItem('isProposalDeleting');
            console.log("Flag de exclusão removida após o carregamento inicial");
          }, 2000);

          try {
            // Extrair parâmetros da URL da API
            const fullUrl = savedApiUrl.startsWith('http') ? savedApiUrl : window.location.origin + savedApiUrl;
            const url = new URL(fullUrl);

            // Extrair a página da URL da API
            const page = url.searchParams.get('page');
            const pageNum = page ? parseInt(page, 10) : 1;

            // Extrair o termo de busca da URL da API
            const searchTerm = url.searchParams.get('search') || '';

            // Extrair os filtros de situação da URL da API
            const situations = url.searchParams.getAll('situation');
            const situationFilter = situations.length > 0 ? situations.join(',') : null;

            console.log("Parâmetros extraídos da URL da API para carregamento inicial:", { pageNum, searchTerm, situationFilter });

            // Atualizar os estados locais
            setSearch(searchTerm);
            setSituationFilter(situationFilter);
            setActiveFilters(prev => ({
              ...prev,
              situation: !!situationFilter
            }));

            // Carregar dados com os parâmetros extraídos
            fetchProposals(pageNum, pagination.pageSize, searchTerm, situationFilter);
            return;
          } catch (error) {
            console.error("Erro ao extrair parâmetros da URL da API:", error);

            // Se houver erro ao extrair parâmetros, usar valores iniciais padrão
            const initialPage = 1;
            const initialSearch = '';
            const initialSituationFilter: string | null = null;

            console.log("Carregamento inicial com valores padrão após erro");

            // Carregar dados iniciais
            fetchProposals(initialPage, pagination.pageSize, initialSearch, initialSituationFilter);
          }
        } else if (isInDeletionProcess) {
          // Se estamos em um processo de exclusão mas não há URL da API salva, usar valores iniciais padrão
          const initialPage = 1;
          const initialSearch = '';
          const initialSituationFilter: string | null = null;

          console.log("Carregamento inicial com valores padrão (processo de exclusão sem URL da API)");

          // Limpar a flag de exclusão, pois não há URL da API salva
          localStorage.removeItem('isProposalDeleting');
          console.log("Flag de exclusão removida (processo de exclusão sem URL da API)");

          // Carregar dados iniciais
          fetchProposals(initialPage, pagination.pageSize, initialSearch, initialSituationFilter);
        }
      } else {
        // Se não estamos no navegador, usar valores iniciais padrão
        const initialPage = 1;
        const initialSearch = '';
        const initialSituationFilter: string | null = null;

        console.log("Carregamento inicial com valores padrão (não estamos no navegador)");

        // Carregar dados iniciais
        fetchProposals(initialPage, pagination.pageSize, initialSearch, initialSituationFilter);
      }

      // Nada a limpar quando o componente for desmontado
      return () => {
        console.log("Componente desmontado");
      };
    }, []);

    const handleSearch = (value: string) => {
      setSearch(value);

      // Limpar a URL da API salva no localStorage quando o usuário está pesquisando
      // Isso é necessário para que a pesquisa funcione corretamente
      if (typeof window !== 'undefined') {
        localStorage.removeItem('lastProposalApiUrl');
      }

      fetchProposals(1, pagination.pageSize, value, situationFilter);
    };

    // Função para lidar com filtros de colunas
    const handleColumnFilter = (columnKey: string, value: any) => {
      if (columnKey === 'situation') {
        // Atualizar o filtro de situação
        setSituationFilter(value);

        // Atualizar filtros ativos
        setActiveFilters(prev => ({
          ...prev,
          situation: !!value
        }));

        // Limpar a URL da API salva no localStorage quando o usuário está filtrando
        // Isso é necessário para que a filtragem funcione corretamente
        if (typeof window !== 'undefined') {
          localStorage.removeItem('lastProposalApiUrl');
        }

        // Buscar dados com o novo filtro
        fetchProposals(1, pagination.pageSize, search, value);

        // Atualizar a página atual no componente pai
        if (onPageChange) onPageChange(1);
      }
    };

    const handleClearFilters = () => {
      // Limpar o estado de pesquisa e filtros
      setSearch("");
      setSituationFilter(null);
      setActiveFilters({});

      // Limpar a URL da API salva no localStorage quando o usuário está limpando os filtros
      // Isso é necessário para que a limpeza de filtros funcione corretamente
      if (typeof window !== 'undefined') {
        localStorage.removeItem('lastProposalApiUrl');
      }

      // Resetar para a primeira página e buscar dados
      fetchProposals(1, pagination.pageSize, "", null);

      // Atualizar a página atual no componente pai
      if (onPageChange) onPageChange(1);

      // Focar no campo de pesquisa após limpar
      setTimeout(() => {
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
      }, 0);
    };

    return (
      <TableGrid
        columns={columns}
        data={data}
        loading={loading}
        pagination={pagination}
        onPaginationChange={(newPage, newPageSize) => {
          console.log("TableGrid.onPaginationChange chamado com:", {
            newPage,
            newPageSize,
            currentPage: pagination.page
          });

          // Evitar chamadas duplicadas verificando se a página ou tamanho da página realmente mudaram
          if (newPage !== pagination.page || newPageSize !== pagination.pageSize) {
            console.log("Atualizando paginação para:", { newPage, newPageSize });

            // Limpar a URL da API salva no localStorage quando o usuário está navegando entre páginas
            // Isso é necessário para que a navegação entre páginas funcione corretamente
            if (typeof window !== 'undefined') {
              localStorage.removeItem('lastProposalApiUrl');
            }

            fetchProposals(newPage, newPageSize, search, situationFilter);
            if (onPageChange) onPageChange(newPage);
          }
        }}
        onSearch={handleSearch}
        searchValue={search}
        searchInputRef={searchInputRef}
        onColumnFilter={handleColumnFilter}
        activeFilters={activeFilters}
        buttonsTemplate={
          <>
            <Button
              className="w-full sm:w-auto bg-blue-500 hover:bg-blue-400"
              onClick={handleClearFilters}
            >
              Limpar filtros <Eraser className="ml-2 h-4 w-4" />
            </Button>
            <Button
              className="w-full sm:w-auto bg-green-500 hover:bg-green-400"
              onClick={onAddClick}
            >
              Adicionar <Plus className="ml-2 h-4 w-4" />
            </Button>
          </>
        }
      />
    );
  }
);

export default ProposalsTable;
