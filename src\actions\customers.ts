"use server";
import { prisma } from "@/src/lib/prisma";
import { Customer } from "@/src/types/core/customer";
import { CustomerSchema } from "@/src/app/views/crm/customers/_schemas/customer.schema";
import { removeFiles } from "./files";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";

export async function loadCustomers() {
  try {
    const { organizationId } = await getCurrentOrganization();

    const data = await prisma.customer.findMany({
      where: { organizationId },
      orderBy: { createdAt: "desc" },
    });

    // Usar o documentType real do banco de dados ou CPF como padrão
    return data.map((customer) => ({
      ...customer,
      documentType: customer.documentType || "CPF",
    }));
  } catch (error) {
    console.error(error);
    throw new Error("Erro ao carregar clientes");
  }
}

export async function loadCustomersPaginated(
  page: number = 1,
  pageSize: number = 10,
  search: string = ""
) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const where = {
      organizationId,
      ...(search
        ? {
            OR: [
              { name: { contains: search, mode: "insensitive" } },
              { email: { contains: search, mode: "insensitive" } },
              { document: { contains: search, mode: "insensitive" } },
              { phone: { contains: search, mode: "insensitive" } },
              ...(search.length > 2
                ? [{ observation: { contains: search, mode: "insensitive" } }]
                : []),
            ],
          }
        : {}),
    };

    const [total, items] = await Promise.all([
      prisma.customer.count({ where }),
      prisma.customer.findMany({
        where,
        orderBy: { createdAt: "desc" },
        skip: (page - 1) * pageSize,
        take: pageSize,
      }),
    ]);

    // Usar o documentType real do banco de dados ou CPF como padrão
    const formattedItems = items.map((customer) => ({
      ...customer,
      documentType: customer.documentType || "CPF",
    }));

    return {
      data: formattedItems,
      page,
      pageSize,
      total,
      totalPages: Math.ceil(total / pageSize),
    };
  } catch (error) {
    console.error("Erro ao carregar clientes paginados:", error);
    throw new Error("Erro ao carregar clientes");
  }
}

export async function findCustomer(id: string) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const data = await prisma.customer.findUnique({
      where: {
        id,
        organizationId,
      },
    });
    if (data) {
      // Usar o documentType real do banco de dados ou CPF como padrão
      return { ...data, documentType: data.documentType || "CPF" } as Customer;
    }
  } catch (error) {
    console.error(error);
  }
}

export async function saveCustomer(customer: CustomerSchema) {
  try {
    const { organizationId } = await getCurrentOrganization();

    let data: Customer;
    if (customer.id) {
      data = (await prisma.customer.update({
        where: { id: customer.id },
        data: {
          name: customer.name,
          email: customer.email,
          document: customer.document,
          phone: customer.phone,
          observation: customer.observation,
          documentType: customer.documentType,
          organizationId,
        },
      })) as Customer;
    } else {
      // Criar um novo cliente sem o id
      const customerData = { ...customer };
      delete customerData.id; // Remover o id se existir

      data = (await prisma.customer.create({
        data: {
          ...customerData,
          organizationId,
        },
      })) as Customer;
    }

    return data;
  } catch (error) {
    console.error("Erro ao salvar cliente:", error);

    const type = (error as any)?.meta?.target?.[0];

    if (type === "email") {
      return {
        error: true,
        message: "Já existe um cliente cadastrado com este email",
      };
    }

    return {
      error: true,
      message: "Erro ao salvar cliente",
    };
  }
}

export async function removeCustomer(customerId: string) {
  try {
    const { organizationId } = await getCurrentOrganization();

    const customer = await prisma.customer.findUnique({
      where: {
        id: customerId,
        organizationId,
      },
    });

    if (!customer) {
      return {
        error: true,
        message: "Cliente não existe ou não pertence a sua organização",
      };
    }

    console.log(`Iniciando remoção do cliente ${customerId}`);

    // 1. Remover propostas e todas as suas dependências
    await removeCustomerProposals(customerId);
    console.log(`Propostas do cliente ${customerId} removidas`);

    // 2. Remover projetos e suas dependências
    await removeCustomerProjects(customerId);
    console.log(`Projetos do cliente ${customerId} removidos`);

    // 3. Remover contatos
    await prisma.contact.deleteMany({ where: { customerId } });
    console.log(`Contatos do cliente ${customerId} removidos`);

    // 4. Finalmente, remover o cliente
    await prisma.customer.delete({
      where: { id: customerId },
    });
    console.log(`Cliente ${customerId} removido com sucesso`);

    return { message: "Cliente removido com sucesso!" };
  } catch (error: any) {
    console.error("Erro ao remover cliente:", error);

    // Verificar se é um erro de chave estrangeira
    if (error?.code === "P2003") {
      const target = error?.meta?.target;
      return {
        error: true,
        message: `Não foi possível excluir o cliente pois existem ${target} associados a ele.`,
      };
    }

    return {
      error: true,
      message:
        "Erro ao remover cliente: " + (error?.message || "Erro desconhecido"),
    };
  }
}

export async function removeCustomerProposals(customerId: string) {
  try {
    console.log(`Buscando propostas do cliente ${customerId}`);
    const proposals = await prisma.proposal.findMany({
      where: { customerId },
      include: {
        file: true,
        plannings: true,
        repairBudgets: true,
        serviceScopes: true,
        contract: true,
        Productivity: true,
        inspectionParameters: {
          include: {
            photos: true,
          },
        },
      },
    });

    const ids = proposals.map((proposal) => proposal.id);
    const files = proposals
      .map((proposal) => proposal.file)
      .filter((file) => file !== null);

    // Coletar todos os IDs de parâmetros de inspeção
    const inspectionParameterIds = proposals
      .flatMap((proposal) => proposal.inspectionParameters)
      .map((param) => param.id);

    // Coletar todos os IDs de fotos
    const photoIds = proposals
      .flatMap((proposal) => proposal.inspectionParameters)
      .flatMap((param) => param.photos)
      .map((photo) => photo.id);

    // Coletar todos os IDs de arquivos de fotos
    const photoFileIds = proposals
      .flatMap((proposal) => proposal.inspectionParameters)
      .flatMap((param) => param.photos)
      .filter((photo) => photo.fileId)
      .map((photo) => photo.fileId);

    console.log(
      `Encontradas ${ids.length} propostas para o cliente ${customerId}`
    );
    console.log(
      `Encontrados ${inspectionParameterIds.length} parâmetros de inspeção`
    );
    console.log(`Encontradas ${photoIds.length} fotos`);

    if (ids.length > 0) {
      // 1. Delete LogProposals related to these proposals FIRST
      await prisma.logProposal.deleteMany({
        where: { proposalId: { in: ids } },
      });
      console.log(`Logs das propostas removidos`);

      // 2. Remover fotos dos parâmetros de inspeção
      if (photoIds.length > 0) {
        await prisma.photo.deleteMany({
          where: { id: { in: photoIds } },
        });
        console.log(`Fotos dos parâmetros de inspeção removidas`);
      }

      // 3. Remover parâmetros de inspeção
      if (inspectionParameterIds.length > 0) {
        await prisma.inspectionParameter.deleteMany({
          where: { id: { in: inspectionParameterIds } },
        });
        console.log(`Parâmetros de inspeção removidos`);
      }

      // 4. Remover produtividades associadas às propostas
      await prisma.productivity.deleteMany({
        where: { proposalId: { in: ids } },
      });
      console.log(`Produtividades das propostas removidas`);

      // 5. Remover contratos associados às propostas
      await prisma.contract.deleteMany({
        where: { proposalId: { in: ids } },
      });
      console.log(`Contratos das propostas removidos`);

      // 6. Remover itens de planejamento de frequência
      await prisma.planningFrequencyItem.deleteMany({
        where: { proposalId: { in: ids } },
      });
      console.log(`Itens de planejamento das propostas removidos`);

      // 7. Remover orçamentos de reparo
      await prisma.repairBudget.deleteMany({
        where: { proposalId: { in: ids } },
      });
      console.log(`Orçamentos de reparo das propostas removidos`);

      // 8. Remover escopos de serviço
      await prisma.serviceScope.updateMany({
        where: { proposalId: { in: ids } },
        data: { proposalId: null },
      });
      console.log(`Escopos de serviço das propostas atualizados`);

      // 9. Remover as propostas
      await prisma.proposal.deleteMany({
        where: { id: { in: ids } },
      });
      console.log(`Propostas removidas`);

      // 10. Remover arquivos associados às propostas
      if (files.length > 0) {
        await removeFiles(files);
        console.log(`Arquivos das propostas removidos`);
      }

      // 11. Remover arquivos associados às fotos
      if (photoFileIds.length > 0) {
        const photoFiles = await prisma.file.findMany({
          where: { id: { in: photoFileIds as string[] } },
        });
        if (photoFiles.length > 0) {
          await removeFiles(photoFiles);
          console.log(`Arquivos das fotos removidos`);
        }
      }
    }

    return;
  } catch (error) {
    console.error("Erro ao remover propostas do cliente:", error);
    throw new Error(
      "Erro ao remover propostas do cliente: " +
        ((error as any)?.message || "Erro desconhecido")
    );
  }
}

export async function removeCustomerProjects(customerId: string) {
  try {
    console.log(`Buscando projetos do cliente ${customerId}`);
    const projects = await prisma.project.findMany({
      where: { customerId },
      include: { projectFiles: { include: { file: true } } },
    });

    const ids = projects.map((project) => project.id);
    // Coletar todos os arquivos associados aos projetos
    const files = projects
      .flatMap((project) => project.projectFiles)
      .map((pf) => pf.file)
      .filter((file) => file !== null);

    console.log(
      `Encontrados ${ids.length} projetos para o cliente ${customerId}`
    );

    if (ids.length > 0) {
      // 1. Remover projetos primeiro
      await prisma.project.deleteMany({
        where: { id: { in: ids } },
      });
      console.log(`Projetos removidos`);

      // 2. Remover arquivos associados
      if (files.length > 0) {
        await removeFiles(files);
        console.log(`Arquivos dos projetos removidos`);
      }
    }

    return;
  } catch (error) {
    console.error("Erro ao remover projetos do cliente:", error);
    throw new Error(
      "Erro ao remover projetos do cliente: " + (error as any)?.message ||
        "Erro desconhecido"
    );
  }
}
