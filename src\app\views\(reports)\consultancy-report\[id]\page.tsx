"use client"
import ContentWrapper from "@/src/components/content-wrapper";
import { But<PERSON> } from "@/src/components/ui/button";
import { Column } from "@/src/components/ui/table-grid";
import { TouchTooltip } from "@/src/components/ui/touch-tooltip";
import { formatDate } from "@/src/lib/utils";
import { InspectionParameter } from "@/src/types/core/inspection-paramenters";
import { FileText, Loader2 } from "lucide-react";
import { useRef, useState } from "react";
import { useRouter } from "next/navigation";
import InspectionReportsTable, { InspectionReportsTableRef } from "../components/inspection-reports-table";
import DownloadReportDialog from "@/src/components/download-report-dialog";
import { generateConsultancyReport } from "@/src/actions/consultancy-report";

type InspectionListPage = {
    params: {
        id: string;
    };
};

export default function ConsultancyInspectionsReports({ params }: InspectionListPage) {
    const [isNavigating, setIsNavigating] = useState(false);
    const [proposalId, setProposalId] = useState<string | null>(null);
    const [inspectionParameterId, setInspectionParameterId] = useState<string | null>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [openUploadDialog, setOpenUploadDialog] = useState(false);
    const tableRef = useRef<InspectionReportsTableRef>(null);
    const router = useRouter();

    const handleNavigation = (url: string) => {
        setIsNavigating(true);
        router.push(url);
    };

    const handleViewReport = (inspectionId: string) => {
        setOpenUploadDialog(true);
        setProposalId(params.id); // ID da proposta vem dos parâmetros da URL
        setInspectionParameterId(inspectionId); // ID do parâmetro de inspeção selecionado
    };

    const handleGenerateReport = (id: string) => {
        // Função placeholder para satisfazer a interface
        console.log("Generate report for inspection:", id);
    };

    const columns: Column<InspectionParameter>[] = [
        {
            key: "numberInspection",
            header: "Nº Inspeção",
            sortable: true,
        },
        {
            key: "technicalData",
            header: "Dados técnicos",
            sortable: true,
            cell: (row) => {
                const technicalData = row.technicalData as string;
                const displayText = technicalData.length > 50 ? technicalData.substring(0, 50) + "..." : technicalData;

                return (
                    <TouchTooltip
                        content={technicalData}
                        maxWidth="400px"
                        className="max-w-[200px] truncate hover:text-blue-500 transition-colors"
                    >
                        {displayText}
                    </TouchTooltip>
                );
            }
        },
        {
            key: "observation",
            header: "Observações",
            sortable: true,
            cell: (row) => {
                const observation = row.observation as string;
                const displayText = observation.length > 50 ? observation.substring(0, 50) + "..." : observation;

                return (
                    <TouchTooltip
                        content={observation}
                        maxWidth="400px"
                        className="max-w-[200px] truncate hover:text-blue-500 transition-colors"
                    >
                        {displayText}
                    </TouchTooltip>
                );
            }
        },
        {
            key: "inspectionDate",
            header: "Data da inspeção",
            cell: (row) => formatDate(row.inspectionDate, "DATE"),
            sortable: true,
        },
        {
            key: "actions",
            header: "Ações",
            cell: (row: any) => (
                <div className="flex justify-start">
                    <button
                        className="p-2 rounded-md hover:bg-gray-100"
                        onClick={() => handleViewReport(row.id)}
                        title="Gerar Relatório de Consultoria"
                    >
                        <FileText className="size-5 text-yellow-500" />
                    </button>
                </div>
            ),
        },
    ];

    return (
        <ContentWrapper title="Inspeções - Consultoria" loading={isNavigating}>
            <InspectionReportsTable
                ref={tableRef}
                columns={columns}
                proposalId={params.id}
                onGenerateReportClick={handleGenerateReport}
                onPageChange={(page) => setCurrentPage(page)}
                buttonsTemplate={
                    <>
                        <Button
                            className="w-full sm:w-auto bg-blue-500 hover:bg-blue-400"
                            onClick={() => tableRef.current?.refresh()}
                        >
                            Limpar filtros <Loader2 className="ml-2 h-4 w-4" />
                        </Button>
                    </>
                }
            />
            <div className="flex justify-end mt-4">
                <Button
                    variant="outline"
                    className="border-blue-500 text-blue-500 hover:bg-blue-50"
                    onClick={() => handleNavigation("/views/consultancy-report")}
                >
                    Voltar
                </Button>
            </div>

            {/* Diálogo de Download de Relatório de Consultoria */}
            <DownloadReportDialog
                reportMergeAction={generateConsultancyReport}
                reportType="CONSULTANCY"
                additionalParamsOptional={{
                    proposalId,
                    inspectionParameterId
                }}
                openUploadDialog={openUploadDialog}
                setOpenUploadDialog={(open) => {
                    setOpenUploadDialog(open);
                    if (!open) {
                        // Recarregar a tabela mantendo a página atual quando o diálogo for fechado
                        tableRef.current?.refresh(currentPage);
                        // Limpar os IDs selecionados
                        setProposalId(null);
                        setInspectionParameterId(null);
                    }
                }}
            />
        </ContentWrapper>
    );
}
