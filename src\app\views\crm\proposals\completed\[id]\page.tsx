"use client";
import { useEffect, useState } from "react";
import { formatDate } from "@/src/lib/utils";
import { proposalSituations } from "@/src/constants";
import { Proposal } from "@/src/types/core/proposal";
import { findProposal } from "@/src/actions/proposals";
import ContentWrapper from "@/src/components/content-wrapper";
import { Button } from "@/src/components/ui/button";
import { useRouter, useSearchParams } from "next/navigation";
import { useToast } from "@/src/hooks/use-toast";
import FileUploadField from "@/src/components/file-upload-field";
import AppConfirmationDialog from "@/src/components/app-confirmation-dialog";
import {
    CheckCircle2,
    Clock,
    FileCheck,
    FileText,
    FilePenLine,
    FileSignature,
    FileWarning,
    FileX,
    Hourglass,
    Pencil,
    Send,
    Paperclip,
    X,
    Download
} from "lucide-react";

interface LogProposal {
    id: string;
    proposalId: string;
    oldStatus: string;
    newStatus: string;
    createdAt: string;
    isCurrentStatus?: boolean;
}

export default function ProposalTimeline({ params }: { params: { id: string } }) {
    const router = useRouter();
    const searchParams = useSearchParams();
    const { toast } = useToast();
    const [proposal, setProposal] = useState<Proposal | null>(null);
    const [logs, setLogs] = useState<LogProposal[]>([]);
    const [loading, setLoading] = useState(true);
    const [attachments, setAttachments] = useState<any[]>([]);
    const [uploadingAttachments, setUploadingAttachments] = useState(false);

    // Determinar de qual tela o usuário veio
    const source = searchParams.get('from') || 'completed';

    // Função para obter o ícone correspondente ao status
    const getStatusIcon = (status: string) => {
        switch (status) {
            case "NEW":
                return <FileText className="h-6 w-6 text-blue-500" />;
            case "UNDER_ANALYSIS":
                return <FilePenLine className="h-6 w-6 text-purple-500" />;
            case "PROPOSAL_SENT":
                return <Send className="h-6 w-6 text-indigo-500" />;
            case "PROPOSAL_ACCEPTED":
                return <FileCheck className="h-6 w-6 text-green-500" />;
            case "SIGN_REQUESTED":
                return <Pencil className="h-6 w-6 text-orange-500" />;
            case "SIGNED":
                return <FileSignature className="h-6 w-6 text-emerald-500" />;
            case "PROJECT_IN_PROGRESS":
                return <Hourglass className="h-6 w-6 text-amber-500" />;
            case "PROJECT_FINISHED":
                return <CheckCircle2 className="h-6 w-6 text-green-600" />;
            case "LOST":
                return <FileX className="h-6 w-6 text-red-500" />;
            default:
                return <Clock className="h-6 w-6 text-gray-500" />;
        }
    };

    // Função para obter a cor de fundo correspondente ao status
    const getStatusColor = (status: string) => {
        switch (status) {
            case "NEW":
                return "bg-blue-100 border-blue-300";
            case "UNDER_ANALYSIS":
                return "bg-purple-100 border-purple-300";
            case "PROPOSAL_SENT":
                return "bg-indigo-100 border-indigo-300";
            case "PROPOSAL_ACCEPTED":
                return "bg-green-100 border-green-300";
            case "SIGN_REQUESTED":
                return "bg-orange-100 border-orange-300";
            case "SIGNED":
                return "bg-emerald-100 border-emerald-300";
            case "PROJECT_IN_PROGRESS":
                return "bg-amber-100 border-amber-300";
            case "PROJECT_FINISHED":
                return "bg-green-100 border-green-300";
            case "LOST":
                return "bg-red-100 border-red-300";
            default:
                return "bg-gray-100 border-gray-300";
        }
    };

    // Função para obter a classe CSS do badge de status (igual ao board do Kanban)
    const getStatusBadgeClass = (status: string) => {
        switch (status) {
            case "NEW":
                return "status-badge-new";
            case "UNDER_ANALYSIS":
                return "status-badge-analysis";
            case "PROPOSAL_SENT":
                return "status-badge-sent";
            case "PROPOSAL_ACCEPTED":
                return "status-badge-accepted";
            case "SIGN_REQUESTED":
                return "status-badge-sign";
            case "SIGNED":
                return "status-badge-signed";
            case "PROJECT_IN_PROGRESS":
                return "status-badge-in-progress";
            case "PROJECT_FINISHED":
                return "status-badge-finished";
            case "LOST":
                return "status-badge-lost";
            default:
                return "";
        }
    };

    const fetchData = async () => {
        try {
            const proposalData = await findProposal(params.id);
            if (proposalData) {
                setProposal(proposalData);
            }

            const response = await fetch(`/api/proposals/${params.id}/logs`);
            if (!response.ok) throw new Error('Failed to fetch logs');
            const logsData = await response.json();

            // Verificar se o status atual está no histórico
            if (proposalData) {
                const currentStatus = proposalData.situation;

                if (logsData.length === 0) {
                    // Se não houver logs, criar um log inicial com o status atual
                    const initialLog = {
                        id: 'initial-status',
                        proposalId: proposalData.id,
                        oldStatus: 'NEW', // Assumindo que o status inicial é NEW
                        newStatus: currentStatus,
                        createdAt: proposalData.createdAt || new Date().toISOString(),
                        isCurrentStatus: true // Marcar como status atual
                    };

                    logsData.push(initialLog);

                    // Registrar o log no servidor para futuras consultas
                    try {
                        await fetch(`/api/proposals/${params.id}/logs`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                oldStatus: 'NEW',
                                newStatus: currentStatus,
                            }),
                        });
                    } catch (logError) {
                        console.error('Error saving initial status log:', logError);
                    }
                } else {
                    const lastLog = logsData[logsData.length - 1];

                    // Se o último status registrado não for o status atual, adicionar o status atual ao histórico
                    if (lastLog.newStatus !== currentStatus) {
                        // Criar um log virtual para o status atual
                        const currentLog = {
                            id: 'current-status',
                            proposalId: proposalData.id,
                            oldStatus: lastLog.newStatus,
                            newStatus: currentStatus,
                            createdAt: new Date().toISOString(),
                            isCurrentStatus: true // Marcar como status atual
                        };

                        // Adicionar o log virtual ao array de logs
                        logsData.push(currentLog);

                        // Registrar o log no servidor para futuras consultas
                        try {
                            await fetch(`/api/proposals/${params.id}/logs`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    oldStatus: lastLog.newStatus,
                                    newStatus: currentStatus,
                                }),
                            });
                        } catch (logError) {
                            console.error('Error saving current status log:', logError);
                        }
                    }
                }
            }

            setLogs(logsData);
        } catch (error) {
            console.error('Error fetching data:', error);
            toast({
                title: "Erro",
                description: "Ocorreu um erro ao carregar o histórico",
                variant: "destructive",
            });
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchData();
        fetchAttachments();
    }, [params.id]);

    const getStatusLabel = (status: string) => {
        return proposalSituations.find(s => s.value === status)?.label || status;
    };

    // Função para buscar anexos
    const fetchAttachments = async () => {
        try {
            const response = await fetch(`/api/proposals/${params.id}/attachments`);
            if (response.ok) {
                const data = await response.json();
                setAttachments(data);
            }
        } catch (error) {
            console.error('Erro ao buscar anexos:', error);
        }
    };

    // Função para adicionar anexos
    const handleFileUpload = async (files: File[] | null) => {
        if (!files || files.length === 0) return;

        setUploadingAttachments(true);
        try {
            const formData = new FormData();
            files.forEach(file => {
                formData.append('files', file);
            });

            const response = await fetch(`/api/proposals/${params.id}/attachments`, {
                method: 'POST',
                body: formData,
            });

            if (response.ok) {
                toast({
                    title: "Sucesso",
                    description: "Anexos adicionados com sucesso!",
                });
                fetchAttachments(); // Recarregar anexos
            } else {
                throw new Error('Erro ao enviar anexos');
            }
        } catch (error) {
            console.error('Erro ao enviar anexos:', error);
            toast({
                title: "Erro",
                description: "Erro ao adicionar anexos",
                variant: "destructive",
            });
        } finally {
            setUploadingAttachments(false);
        }
    };

    // Função para remover anexo
    const handleRemoveAttachment = async (fileId: string) => {
        try {
            const response = await fetch(`/api/proposals/${params.id}/attachments?fileId=${fileId}`, {
                method: 'DELETE',
            });

            if (response.ok) {
                toast({
                    title: "Sucesso",
                    description: "Anexo removido com sucesso!",
                });
                fetchAttachments(); // Recarregar anexos
            } else {
                throw new Error('Erro ao remover anexo');
            }
        } catch (error) {
            console.error('Erro ao remover anexo:', error);
            toast({
                title: "Erro",
                description: "Erro ao remover anexo",
                variant: "destructive",
            });
        }
    };

    // Função para fazer download do anexo
    const handleDownloadAttachment = async (fileId: string, fileName: string) => {
        try {
            const response = await fetch(`/api/files/${fileId}`);
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            }
        } catch (error) {
            console.error('Erro ao fazer download:', error);
            toast({
                title: "Erro",
                description: "Erro ao fazer download do arquivo",
                variant: "destructive",
            });
        }
    };

    return (
        <ContentWrapper
            loading={loading}
            title={`${proposal?.name || "Proposta"}`}
        >
            {/* Adicionar estilo personalizado ao título */}
            <style jsx global>{`
                .content-wrapper-title {
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
                }
            `}</style>

            {/* Aplicar classe personalizada ao título */}
            <script dangerouslySetInnerHTML={{
                __html: `
                document.addEventListener('DOMContentLoaded', function() {
                    const title = document.querySelector('h1.text-3xl.font-semibold.text-green-500');
                    if (title) {
                        title.classList.add('content-wrapper-title');
                    }
                });
            `}} />

            <div className="flex items-center mb-6">
                <Button
                    variant="ghost"
                    className="flex items-center text-blue-600 hover:text-blue-800 hover:bg-blue-50 transition-all duration-200"
                    onClick={() => {
                        // Determinar para qual página voltar com base no parâmetro 'from'
                        const routes = {
                            'completed': '/views/crm/proposals/completed',
                            'lost': '/views/crm/proposals/lost',
                            'to-start': '/views/crm/proposals/to-start',
                            'accepted': '/views/crm/proposals/accepted'
                        };
                        router.push(routes[source as keyof typeof routes] || '/views/crm/proposals/completed');
                    }}
                >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                    </svg>
                    <span className="font-medium">Voltar</span>
                </Button>
                <div className="h-0.5 flex-grow bg-gray-100 ml-4"></div>
            </div>

            {proposal && (
                <div className="mb-6 bg-white rounded-lg shadow overflow-hidden">
                    <div className="p-4 bg-gradient-to-r from-green-300 via-green-400 to-green-300 text-green-800 shadow-md rounded-t-lg">
                        <h3 className="text-xl font-bold">Detalhes do Projeto</h3>
                    </div>

                    <div className="p-5">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div className="p-4 bg-green-50 rounded-lg border border-green-100 shadow-md hover:shadow-lg transition-shadow duration-300">
                                <p className="text-sm text-green-600 font-medium mb-1">Cliente</p>
                                <p className="font-semibold text-slate-800">{proposal.customer?.name}</p>
                            </div>

                            <div className="p-4 bg-green-50 rounded-lg border border-green-100 shadow-md hover:shadow-lg transition-shadow duration-300">
                                <p className="text-sm text-green-600 font-medium mb-1">Status Atual</p>
                                <div className="flex items-center">
                                    <span className={`status-badge ${getStatusBadgeClass(proposal.situation)}`}>
                                        {getStatusLabel(proposal.situation)}
                                    </span>
                                </div>
                            </div>

                            <div className="p-4 bg-green-50 rounded-lg border border-green-100 shadow-md hover:shadow-lg transition-shadow duration-300">
                                <p className="text-sm text-green-600 font-medium mb-1">Data de Início</p>
                                <p className="font-semibold text-slate-800">{formatDate(proposal.startDate)}</p>
                            </div>

                            <div className="p-4 bg-green-50 rounded-lg border border-green-100 shadow-md hover:shadow-lg transition-shadow duration-300">
                                <p className="text-sm text-green-600 font-medium mb-1">
                                    {proposal.situation === "PROJECT_FINISHED" ? "Data de Conclusão" : "Data de Conclusão Prevista"}
                                </p>
                                <p className="font-semibold text-slate-800">{formatDate(proposal.endDate)}</p>
                            </div>

                            {proposal.budget && (
                                <div className="p-4 bg-green-50 rounded-lg border border-green-100 shadow-md hover:shadow-lg transition-shadow duration-300">
                                    <p className="text-sm text-green-600 font-medium mb-1">Orçamento</p>
                                    <p className="font-semibold text-slate-800">R$ {Number(proposal.budget).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</p>
                                </div>
                            )}

                            {proposal.area && (
                                <div className="p-4 bg-green-50 rounded-lg border border-green-100 shadow-md hover:shadow-lg transition-shadow duration-300">
                                    <p className="text-sm text-green-600 font-medium mb-1">Área</p>
                                    <p className="font-semibold text-slate-800">{Number(proposal.area).toLocaleString('pt-BR')} m²</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            )}

            {/* Seção de Anexos */}
            <div className="mb-6 bg-white rounded-lg shadow overflow-hidden">
                <div className="p-4 bg-gradient-to-r from-blue-300 via-blue-400 to-blue-300 text-blue-800 shadow-md rounded-t-lg">
                    <h3 className="text-xl font-bold flex items-center">
                        <Paperclip className="h-6 w-6 mr-2" />
                        Anexos da Proposta
                    </h3>
                </div>

                <div className="p-5">
                    {/* Upload de arquivos */}
                    <div className="mb-6">
                        <FileUploadField
                            onFileChange={handleFileUpload}
                            label="Arraste e solte arquivos ou clique para selecionar"
                            multiple={true}
                            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt"
                        />
                        {uploadingAttachments && (
                            <div className="mt-2 text-sm text-blue-600">
                                Enviando anexos...
                            </div>
                        )}
                    </div>

                    {/* Lista de anexos */}
                    {attachments.length > 0 ? (
                        <div className="space-y-3">
                            <h4 className="text-lg font-semibold text-gray-700 mb-3">
                                Arquivos anexados ({attachments.length})
                            </h4>
                            {attachments.map((attachment) => (
                                <div
                                    key={attachment.fileId}
                                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors"
                                >
                                    <div className="flex items-center space-x-3">
                                        <FileText className="h-5 w-5 text-blue-500" />
                                        <div>
                                            <p className="font-medium text-gray-900">
                                                {attachment.file.name}
                                            </p>
                                            <p className="text-sm text-gray-500">
                                                {(attachment.file.size / 1024 / 1024).toFixed(2)} MB •
                                                Enviado em {formatDate(attachment.file.uploadedAt)}
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleDownloadAttachment(attachment.file.id, attachment.file.name)}
                                            className="text-blue-600 hover:text-blue-800"
                                        >
                                            <Download className="h-4 w-4" />
                                        </Button>
                                        <AppConfirmationDialog
                                            title="Excluir anexo"
                                            description={`Tem certeza que deseja excluir o arquivo "${attachment.file.name}"? Esta ação não pode ser desfeita.`}
                                            onConfirmCallback={() => handleRemoveAttachment(attachment.file.id)}
                                            dialogCancelClassName="bg-blue-500 hover:bg-blue-600 text-white hover:text-white"
                                            dialogActionClassName="bg-red-500 hover:bg-red-600 text-white hover:text-white"
                                            confirmButtonText="Excluir"
                                        >
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                className="text-red-600 hover:text-red-800"
                                            >
                                                <X className="h-4 w-4" />
                                            </Button>
                                        </AppConfirmationDialog>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="text-center py-8 text-gray-500">
                            <Paperclip className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                            <p>Nenhum anexo adicionado ainda</p>
                            <p className="text-sm">Use o campo acima para adicionar arquivos à proposta</p>
                        </div>
                    )}
                </div>
            </div>

            <div className="bg-white rounded-lg shadow overflow-hidden">
                <div className="p-4 bg-gradient-to-r from-green-300 via-green-400 to-green-300 text-green-800 shadow-md rounded-t-lg">
                    <h3 className="text-xl font-bold">Linha do Tempo</h3>
                </div>
                <div className="p-6">
                    {logs.length === 0 ? (
                        <div className="flex flex-col items-center justify-center py-12 bg-gray-50 rounded-lg border border-gray-100">
                            <FileWarning className="h-20 w-20 text-green-400 mb-4" />
                            <p className="text-gray-700 text-xl font-medium">Nenhum histórico de status encontrado</p>
                            <p className="text-gray-500 text-sm mt-2 max-w-md text-center">O histórico será exibido automaticamente quando houver mudanças de status na proposta</p>
                        </div>
                    ) : (
                        <div className="space-y-8 relative before:absolute before:inset-0 before:ml-8 before:w-0.5 before:-translate-x-1/2 before:bg-gradient-to-b before:from-transparent before:via-slate-300 before:to-transparent">
                            {logs.map((log, index) => (
                                <div key={log.id} className={`relative flex items-start gap-6 pl-16 ${log.isCurrentStatus ? 'animate-pulse' : ''}`}>
                                    {/* Ícone do status */}
                                    <div className={`absolute left-0 flex items-center justify-center w-16 h-16 rounded-full border-2 ${getStatusColor(log.newStatus)} shadow-md`}>
                                        <div className="flex items-center justify-center w-12 h-12 rounded-full bg-white">
                                            {getStatusIcon(log.newStatus)}
                                        </div>
                                    </div>

                                    {/* Conteúdo do log */}
                                    <div className={`flex flex-col p-4 rounded-lg border ${getStatusColor(log.newStatus)} w-full shadow-md hover:shadow-lg transition-shadow duration-300`}>
                                        {/* Data e status */}
                                        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                                            <div className="text-sm font-medium text-slate-700 mb-1 md:mb-0">
                                                {formatDate(log.createdAt, "DATETIME")}
                                            </div>
                                            <div className="text-sm font-semibold flex items-center">
                                                {log.isCurrentStatus && (
                                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">
                                                        Status Atual
                                                    </span>
                                                )}
                                                <span className={`status-badge ${getStatusBadgeClass(log.newStatus)}`}>
                                                    {getStatusLabel(log.newStatus)}
                                                </span>
                                            </div>
                                        </div>

                                        {/* Descrição da mudança */}
                                        <div className="text-slate-700 mt-1 flex items-center flex-wrap gap-2">
                                            <span>Status alterado de</span>
                                            <span className={`status-badge ${getStatusBadgeClass(log.oldStatus)}`}>
                                                {getStatusLabel(log.oldStatus)}
                                            </span>
                                            <span>para</span>
                                            <span className={`status-badge ${getStatusBadgeClass(log.newStatus)}`}>
                                                {getStatusLabel(log.newStatus)}
                                            </span>
                                        </div>

                                        {/* Indicador de posição na linha do tempo */}
                                        {index < logs.length - 1 && (
                                            <div className="absolute left-8 top-16 h-[calc(100%-4rem)] w-0.5 bg-slate-200"></div>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </ContentWrapper>
    );
}



