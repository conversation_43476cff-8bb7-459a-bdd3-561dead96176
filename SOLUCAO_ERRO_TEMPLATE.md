# Solução para o Erro: "Raw tag not in paragraph"

## 🎯 Problema Identificado

Você estava usando esta estrutura no template:
```
{{#primeiraImagem}}{{%image}}{{/primeiraImagem}}
```

**Por que deu erro:**
- O loop `{{#primeiraImagem}}...{{/primeiraImagem}}` altera a estrutura XML do documento
- A tag de imagem `{{%image}}` dentro do loop não consegue encontrar o parágrafo correto
- O docxtemplater não consegue processar imagens dentro de loops condicionais

## ✅ Solução Implementada

### 1. Código Ajustado Automaticamente
Modifiquei o arquivo `src/actions/inspection-report.ts` para fornecer duas variáveis:

- **`primeiraImagem`**: URL direta da primeira imagem (para tag simples)
- **`primeiraImagemObj`**: Objeto completo com imagem, número e legenda

### 2. Como Usar no Template

**❌ Substitua isto:**
```
{{#primeiraImagem}}{{%image}}{{/primeiraImagem}}
```

**✅ Por isto:**
```
{{primeiraImagem}}
```

### 3. O que Acontece Agora

Quando você usar `{{primeiraImagem}}` no template, o sistema vai:
1. Buscar a primeira foto dos parâmetros de inspeção
2. Retornar a URL completa da imagem: `https://minio.../caminho-da-imagem.jpg`
3. Inserir a imagem diretamente no documento

## 🔧 Passos para Corrigir

### 1. Editar o Template
1. Abra seu template DOCX no Microsoft Word
2. Use Ctrl+F para procurar: `{{#primeiraImagem}}{{%image}}{{/primeiraImagem}}`
3. Substitua por: `{{primeiraImagem}}`
4. Salve o template

### 2. Testar a Correção
1. Faça upload do template corrigido
2. Gere um relatório de teste
3. Verifique se a primeira imagem aparece corretamente

## 📋 Alternativas Disponíveis

Se você precisar de mais controle sobre a imagem:

### Opção A: Apenas a URL (Recomendada)
```
{{primeiraImagem}}
```

### Opção B: Objeto completo (se precisar de legenda)
```
{{primeiraImagemObj.image}}
Legenda: {{primeiraImagemObj.legenda}}
```

### Opção C: Loop com objeto (se realmente precisar)
```
{{#primeiraImagemObj}}
{{%image}}
Legenda: {{legenda}}
{{/primeiraImagemObj}}
```

## 🚀 Melhorias Implementadas

### 1. Detecção Automática do Erro
- Sistema detecta automaticamente o erro de loop com imagem
- Mostra mensagem específica com dica de solução
- Orienta sobre como corrigir o template

### 2. Mensagens de Erro Melhoradas
Agora quando houver erro, você verá:
```
Erro no template: A tag "image" não está dentro de um parágrafo válido.
DICA: Se você está usando {{#primeiraImagem}}{{%image}}{{/primeiraImagem}}, 
tente usar apenas {{primeiraImagem}} (sem o loop) ou mova a tag para fora do loop.
```

### 3. Logs Detalhados
- Logs com prefixo `[DOCX MERGE]` para facilitar debugging
- Medição de tempo de processamento
- Rastreamento de erros específicos

## 📝 Resumo da Solução

1. **Problema**: Loop com imagem causava erro de estrutura XML
2. **Solução**: Variável `primeiraImagem` agora retorna URL direta
3. **Template**: Use `{{primeiraImagem}}` em vez do loop
4. **Resultado**: Imagem inserida corretamente sem erros

## 🔍 Para Verificar se Funcionou

1. **Logs de Sucesso**: Procure por `[DOCX MERGE] Merge concluído com sucesso`
2. **Arquivo Gerado**: Verifique se o relatório foi criado
3. **Imagem Visível**: Confirme que a primeira imagem aparece no documento
4. **Sem Erros**: Não deve haver mais erros de "Raw tag not in paragraph"

A solução está implementada e pronta para uso! 🎉
