/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "standalone",
  // reactStrictMode: false, // Re-render for dev
  reactStrictMode: true,
  experimental: {
    serverActions: {
      bodySizeLimit: '200mb',
      // Aumentar o timeout para 5 minutos para operações longas como geração de relatórios
      allowedOrigins: ['localhost:3000', 'ageu.eng.br'],
    },
  },
  // Configurações para melhorar performance com documentos grandes
  serverRuntimeConfig: {
    // Timeout de 5 minutos para operações longas
    maxDuration: 300,
  },
  webpack: (config, { isServer }) => {
    // Adiciona a configuração do canvas do next.config.js
    config.externals = [...(config.externals || []), { canvas: "canvas" }];

    if (!isServer) {
      // Não incluir o nodemailer no bundle do cliente
      config.resolve.fallback = {
        ...config.resolve.fallback,
        nodemailer: false,
      };
    }
    return config;
  },

}

module.exports = nextConfig;
