import { NextRequest, NextResponse } from "next/server";
import { generateInspectionReport } from "@/src/actions/inspection-parameters";
import { generateProjectReport } from "@/src/actions/project-report";
import { generateConsultancyReport } from "@/src/actions/consultancy-report";

export const dynamic = "force-dynamic";
export const revalidate = 0;

// Configurar timeout de 5 minutos para esta rota
export const maxDuration = 300;

interface ReportGenerationRequest {
  reportTemplateId: string;
  reportType: "INSPECTION" | "PROJECT" | "CONSULTANCY";
  params: any;
}

export async function POST(request: NextRequest) {
  try {
    console.log("[REPORT API] Iniciando geração de relatório via API...");
    
    const body: ReportGenerationRequest = await request.json();
    const { reportTemplateId, reportType, params } = body;

    if (!reportTemplateId || !reportType || !params) {
      return NextResponse.json(
        { error: "Parâmetros obrigatórios: reportTemplateId, reportType, params" },
        { status: 400 }
      );
    }

    console.log(`[REPORT API] Tipo de relatório: ${reportType}, Template: ${reportTemplateId}`);

    let result;
    const startTime = Date.now();

    try {
      switch (reportType) {
        case "INSPECTION":
          result = await generateInspectionReport(reportTemplateId, params);
          break;
        case "PROJECT":
          result = await generateProjectReport(reportTemplateId, params);
          break;
        case "CONSULTANCY":
          result = await generateConsultancyReport(reportTemplateId, params);
          break;
        default:
          return NextResponse.json(
            { error: `Tipo de relatório não suportado: ${reportType}` },
            { status: 400 }
          );
      }

      const processingTime = Date.now() - startTime;
      console.log(`[REPORT API] Relatório gerado com sucesso em ${processingTime}ms`);

      return NextResponse.json({
        success: true,
        fileEditorId: result?.fileEditorId,
        processingTime,
        message: "Relatório gerado com sucesso"
      });

    } catch (generationError) {
      const processingTime = Date.now() - startTime;
      console.error(`[REPORT API] Erro na geração após ${processingTime}ms:`, generationError);
      
      // Verificar se é erro de timeout
      const errorMessage = generationError instanceof Error ? generationError.message : "Erro desconhecido";
      const isTimeoutError = errorMessage.toLowerCase().includes('timeout') || 
                            errorMessage.toLowerCase().includes('time') ||
                            errorMessage.toLowerCase().includes('exceeded');

      return NextResponse.json(
        {
          error: isTimeoutError 
            ? "O template é muito extenso e excedeu o tempo limite. Tente usar um template menor."
            : `Erro na geração do relatório: ${errorMessage}`,
          processingTime,
          isTimeout: isTimeoutError
        },
        { status: isTimeoutError ? 408 : 500 }
      );
    }

  } catch (error) {
    console.error("[REPORT API] Erro geral na API:", error);
    return NextResponse.json(
      { 
        error: "Erro interno do servidor",
        details: error instanceof Error ? error.message : "Erro desconhecido"
      },
      { status: 500 }
    );
  }
}
