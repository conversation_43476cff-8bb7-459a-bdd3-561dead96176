"use client";
import { useEffect, useRef, useState } from "react";
import ContractsInProgressTable, { ContractsInProgressTableRef } from "./_components/contracts-in-progress-table";
import { useRouter } from "next/navigation";
// import { loadProposals } from "@/src/actions/proposals";
import ContentWrapper from "@/src/components/content-wrapper";
import { formatCurrency, formatDate } from "@/src/lib/utils";
import { Customer } from "@/src/types/core/customer";
import { Proposal, ProposalSituation } from "@/src/types/core/proposal";
import {
  CircleDollarSign,
  Search,
  ChartSpline,
  Eye,
} from "lucide-react";
import { Column } from "@/src/components/ui/table-grid";
import { proposalSituations, serviceTypeOptions } from "@/src/constants";

export default function AcceptedProposals() {
  const [loading, setLoading] = useState(true);
  const [isNavigating, setIsNavigating] = useState(false);
  const router = useRouter();
  const [customers, setCustomers] = useState<Customer[]>([]);
  // const [currentPage, setCurrentPage] = useState(1);
  const tableRef = useRef<ContractsInProgressTableRef>(null);

  const fetchCustomers = async () => {
    try {
      const res = await fetch("/api/customers?pageSize=1000");
      if (!res.ok) throw new Error("Erro ao carregar clientes");

      const result = await res.json();
      console.log("Dados de clientes recebidos:", result);

      // Verificar se os dados estão na propriedade 'data' ou diretamente no resultado
      const customers = result.data || result.items || result;

      // Garantir que customers é um array
      if (!Array.isArray(customers)) {
        console.error("Dados de clientes não são um array:", customers);
        return [];
      }

      return customers as Customer[];
    } catch (error) {
      console.error("Erro ao carregar clientes:", error);
      return [];
    }
  };



  const handleNavigation = (url: string) => {
    setIsNavigating(true);
    router.push(url);
  };



  // Usando função normal em vez de useCallback para evitar problemas
  async function loadData() {
    setLoading(true);
    try {
      const customersData = await fetchCustomers();
      setCustomers(customersData || []);
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
    } finally {
      setLoading(false);
    }
  }

  // Carregar dados quando o componente for montado
  useEffect(() => {
    loadData();
  }, []);


  const columns: Column<Proposal>[] = [
    {
      key: "customer",
      header: "Cliente",
      cell: (row) => (row.customer as Customer).name,
      sortable: true,
      filterable: true,
      filterOptions: Array.isArray(customers) ? customers.map((c) => ({
        label: c.name,
        value: c.id,
      })) : [],
      getFilterValue: (row) => (row.customer as Customer).id,
    },
    {
      key: "name",
      header: "Projeto",
      sortable: true,
    },
    {
      key: "budget",
      header: "Orçamento",
      cell: (row) => formatCurrency(row.budget),
      sortable: true,
    },
    {
      key: "startDate",
      header: "Data início",
      cell: (row) => formatDate(row.startDate, "DATE"),
      sortable: true,
    },
    {
      key: "endDate",
      header: "Data de conclusão prevista",
      cell: (row) => formatDate(row.endDate, "DATE"),
      sortable: true,
    },
    {
      key: "serviceType",
      header: "Tipo de Serviço",
      cell: (row) => {
        const serviceTypeLabel = serviceTypeOptions.find(
          (option) => option.value === row.serviceType
        )?.label || row.serviceType || '';

        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-600">
            {serviceTypeLabel}
          </span>
        );
      },
      sortable: true,
    },
    {
      key: "situation",
      header: "Situação",
      cell: (row) => {
        const rowValue = row.situation as ProposalSituation;
        const situationLabel = proposalSituations.find(
          (situation) => situation.value == rowValue
        )?.label || '';

        // Determinar a classe do badge com base na situação
        let badgeClass = '';

        switch (rowValue) {
          case 'SIGNED':
            badgeClass = 'status-badge-signed';
            break;
          case 'PROJECT_IN_PROGRESS':
            badgeClass = 'status-badge-in-progress';
            break;
          default:
            badgeClass = '';
        }

        return (
          <span className={`status-badge ${badgeClass}`}>
            {situationLabel}
          </span>
        );
      },
      sortable: true,
      filterable: true,
      filterOptions: [
        {
          label: "Assinado",
          value: "SIGNED",
        },
        {
          label: "Projeto em andamento",
          value: "PROJECT_IN_PROGRESS",
        },
      ],
      getFilterValue: (row) => row.situation as string,
    },
    {
      key: "actions",
      header: "Ações",
      cell: (row) => {
        // Verificar se o contrato é do tipo Fiscalização ou Gerenciamento
        const isFiscalizacaoOrGerenciamento =
          row.serviceType === "FISCALIZACAO" ||
          row.serviceType === "GERENCIAMENTO";

        return (
          <div className="flex gap-3">
            {/* Ícone de visualização - sempre visível */}
            <span title="Visualizar contrato">
              <Eye
                className="size-5 text-green-500 cursor-pointer"
                onClick={() =>
                  handleNavigation(`/views/crm/proposals/completed/${row.id}?from=accepted`)
                }
              />
            </span>

            {/* Ícone de parâmetros de inspeção - sempre visível */}
            <span title="Parâmetros de inspeção">
              <Search
                className="size-5 text-blue-500 cursor-pointer"
                onClick={() =>
                  handleNavigation(`/views/crm/proposals/inspection-parameters?id=${row.id}`)
                }
              />
            </span>

            {/* Ícones visíveis apenas para contratos de Fiscalização e Gerenciamento */}
            {isFiscalizacaoOrGerenciamento && (
              <>
                {/* Ícone de orçamento de serviços */}
                <span title="Orçamento de serviços">
                  <CircleDollarSign
                    className="size-5 text-green-500 cursor-pointer"
                    onClick={() =>
                      handleNavigation(`/views/crm/proposals/services-budget?id=${row.id}`)
                    }
                  />
                </span>



                {/* Ícone de histograma */}
                <span title="Histograma">
                  <ChartSpline
                    className="size-5 text-amber-500 cursor-pointer"
                    onClick={() =>
                      handleNavigation(`/views/crm/proposals/histogram/services?id=${row.id}`)
                    }
                  />
                </span>
              </>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <ContentWrapper
      title="Contratos em andamento"
      loading={loading || isNavigating}
    >
      <ContractsInProgressTable
        ref={tableRef}
        columns={columns}
      // onPageChange={(page) => setCurrentPage(page)}
      />
    </ContentWrapper>
  );
}
