# Guia de Solução de Problemas - Templates DOCX

## Erro: "A tag 'image' não está dentro de um parágrafo válido"

### Problema Específico: Loop com Imagem
**Estrutura problemática**: `{{#primeiraImagem}}{{%image}}{{/primeiraImagem}}`

Este erro ocorre porque:
- O loop `{{#primeiraImagem}}...{{/primeiraImagem}}` altera a estrutura XML do documento
- A tag de imagem `{{%image}}` dentro do loop não consegue encontrar o parágrafo correto
- O docxtemplater não consegue processar imagens dentro de loops condicionais

### ✅ Soluções Recomendadas

#### Opção 1: Usar Tag Simples (Recomendado)
**❌ Problemático:**
```
{{#primeiraImagem}}{{%image}}{{/primeiraImagem}}
```

**✅ Solução:**
```
{{primeiraImagem}}
```

**Como implementar:**
1. Abra o template DOCX no Word
2. Substitua `{{#primeiraImagem}}{{%image}}{{/primeiraImagem}}` por `{{primeiraImagem}}`
3. No código, passe a URL da imagem diretamente na variável `primeiraImagem`

#### Opção 2: Mover a Tag para Fora do Loop
**✅ Alternativa:**
```
{{#primeiraImagem}}
Texto do loop aqui
{{/primeiraImagem}}

{{%image}}
```

#### Opção 3: Usar Condição Diferente
**✅ Outra alternativa:**
```
{{#temImagem}}{{%primeiraImagem}}{{/temImagem}}
```

### Implementação no Código

**✅ SOLUÇÃO IMPLEMENTADA**: O código foi ajustado automaticamente!

Agora você tem duas opções disponíveis:

```javascript
// ✅ Opção 1: Tag simples (RECOMENDADA)
// Use no template: {{primeiraImagem}}
const variables = {
  primeiraImagem: "https://minio.../caminho-da-imagem.jpg" // URL direta
};

// ✅ Opção 2: Objeto completo (se precisar de mais dados)
// Use no template: {{primeiraImagemObj.image}} ou {{#primeiraImagemObj}}{{%image}}{{/primeiraImagemObj}}
const variables = {
  primeiraImagemObj: {
    image: "https://minio.../caminho-da-imagem.jpg",
    numero: 1,
    legenda: "Descrição da imagem"
  }
};
```

**Para resolver seu problema específico:**
1. No template, substitua `{{#primeiraImagem}}{{%image}}{{/primeiraImagem}}`
2. Por: `{{primeiraImagem}}` (tag simples)
3. O sistema agora fornece a URL direta da imagem

## Outras Causas do Erro

### 1. Tag em Local Inadequado
- **Problema**: Tag inserida em tabela, cabeçalho, rodapé
- **Solução**: Mover para parágrafo de texto normal

### 2. Formatação Incorreta
- **Problema**: Tag com formatação especial (negrito, itálico)
- **Solução**: Remover formatação, deixar texto simples

### 3. Sintaxe Incorreta
- **Correto**: `{{variavel}}` ou `{{%image}}`
- **Incorreto**: `{variavel}`, `{{variavel}`, `%variavel%`

## Como Testar a Correção

### 1. Teste Simples
1. Substitua a tag problemática pela versão simples
2. Teste com uma imagem pequena
3. Verifique se a geração funciona

### 2. Verificação Visual
1. Abra o template no Word
2. Use Ctrl+F para procurar tags restantes
3. Certifique-se de que não há loops com imagens

### 3. Teste Completo
1. Gere o relatório com dados reais
2. Verifique se a imagem aparece corretamente
3. Confirme que não há erros no console

## Exemplo Prático

### Cenário: Mostrar primeira imagem de uma inspeção

**❌ Estrutura problemática:**
```
Primeira imagem da inspeção:
{{#primeiraImagem}}{{%image}}{{/primeiraImagem}}
```

**✅ Estrutura corrigida:**
```
Primeira imagem da inspeção:
{{primeiraImagem}}
```

**Código para gerar variáveis:**
```javascript
// Buscar a primeira imagem dos parâmetros de inspeção
const primeiraImagem = inspectionParameters[0]?.photos?.[0]?.url || "";

const variables = {
  // ... outras variáveis
  primeiraImagem: primeiraImagem
};
```

## Dicas Importantes

1. **Evite loops com imagens**: Use tags simples sempre que possível
2. **Teste incrementalmente**: Adicione uma tag por vez
3. **Use imagens pequenas**: Para testes, use imagens < 100KB
4. **Mantenha estrutura simples**: Evite formatação complexa ao redor das tags
5. **Documente as tags**: Mantenha lista das variáveis usadas no template

## Se o Problema Persistir

1. **Verifique os logs**: Procure por `[DOCX MERGE]` nos logs do sistema
2. **Teste com template mínimo**: Crie template simples só com a tag problemática
3. **Valide a URL da imagem**: Certifique-se de que a imagem é acessível
4. **Entre em contato**: Inclua a estrutura exata da tag que está causando problema
