"use server";

import { persistFileEditorFromBuffer } from "@/src/actions/file-editor";
import { getBufferFromFile<PERSON>ey } from "@/src/lib/minio";
import { prisma } from "@/src/lib/prisma";
import {
  FileEditorFileType,
  FileEditorMimetypesMap,
} from "@/src/types/core/file-editor";
import { mergeVariablesDocx } from "./replacers/docx-variable-replace";
import { mergeVariablesXlsx } from "./replacers/xlsx-variable-replace";

export async function replaceFromFileEditorIdAndVariables(
  fileEditorId: string,
  variables: Record<string, any>,
  fileName?: string
) {
  console.log(
    `[REPORT GENERATION] Iniciando geração de relatório para fileEditorId: ${fileEditorId}`
  );

  const fileEditor = await prisma.fileEditor.findUnique({
    where: { id: fileEditorId },
  });
  if (!fileEditor) {
    throw Error("File not exists");
  }
  if (!variables) {
    throw Error("There is no data to replace");
  }
  if (!fileName) {
    fileName = `${Date.now()}-file-editor-replaced`;
  }

  console.log(
    `[REPORT GENERATION] Arquivo encontrado: ${fileEditor.filename}, tipo: ${fileEditor.mimetype}`
  );
  console.log(`[REPORT GENERATION] Iniciando processamento do template...`);

  const startTime = Date.now();
  const replacedBuffer = await replaceAndGetBufferFromFileKey(
    fileEditor.key,
    fileEditor.mimetype,
    variables
  );
  const processingTime = Date.now() - startTime;

  console.log(`[REPORT GENERATION] Template processado em ${processingTime}ms`);
  console.log(`[REPORT GENERATION] Salvando arquivo final...`);

  const result = await persistFileEditorFromBuffer(
    replacedBuffer,
    fileName,
    fileEditor.mimetype
  );

  console.log(
    `[REPORT GENERATION] Relatório gerado com sucesso: ${result?.id}`
  );
  return result;
}

async function replaceAndGetBufferFromFileKey(
  fileKey: string,
  fileType: string,
  variables: Record<string, any>
) {
  console.log(`[REPORT GENERATION] Processando arquivo tipo: ${fileType}`);

  try {
    if (
      FileEditorMimetypesMap.get(FileEditorFileType.DOCX)?.includes(fileType)
    ) {
      console.log(`[REPORT GENERATION] Baixando arquivo DOCX do storage...`);
      const bufferFile = await getBufferFromFileKey(fileKey);
      console.log(
        `[REPORT GENERATION] Arquivo baixado, tamanho: ${bufferFile.length} bytes`
      );
      console.log(`[REPORT GENERATION] Iniciando merge de variáveis DOCX...`);
      const result = await mergeVariablesDocx(bufferFile, variables);
      console.log(
        `[REPORT GENERATION] Merge DOCX concluído, tamanho final: ${result.length} bytes`
      );
      return result;
    }

    if (
      FileEditorMimetypesMap.get(FileEditorFileType.XLSX)?.includes(fileType)
    ) {
      console.log(`[REPORT GENERATION] Baixando arquivo XLSX do storage...`);
      const bufferFile = await getBufferFromFileKey(fileKey);
      console.log(
        `[REPORT GENERATION] Arquivo baixado, tamanho: ${bufferFile.length} bytes`
      );
      console.log(`[REPORT GENERATION] Iniciando merge de variáveis XLSX...`);
      const result = await mergeVariablesXlsx(bufferFile, variables);
      console.log(
        `[REPORT GENERATION] Merge XLSX concluído, tamanho final: ${result.length} bytes`
      );
      return result;
    }

    throw Error("Type not supported:" + fileType);
  } catch (error) {
    console.error(`[REPORT GENERATION] Erro durante processamento:`, error);
    throw error;
  }
}
