# Layout de Imagens nos Templates

Este documento explica como controlar o layout das imagens nos templates de relatório.

## Variáveis Disponíveis

### 1. `items` - Imagens em lista vertical (padr<PERSON>)
```
{{#items}}
{{%image}}
Imagem {{numero}} – {{legenda}}

{{/items}}
```
**Resultado**: Imagens uma embaixo da outra (layout vertical)

### 2. `primeiraImagem` - Apenas a primeira imagem
```
{{#primeiraImagem}}
{{%image}}
Imagem {{numero}} – {{legenda}}
{{/primeiraImagem}}
```
**Resultado**: Mostra apenas a primeira imagem (útil para destaque)

### 3. `itemsPares` - Imagens em pares lado a lado
```
{{#itemsPares}}
{{#imagem1}}{{%image}}{{/imagem1}} {{#imagem2}}{{%image}}{{/imagem2}}
{{#imagem1}}Imagem {{numero}} – {{legenda}}{{/imagem1}} {{#imagem2}}Imagem {{numero}} – {{legenda}}{{/imagem2}}

{{/itemsPares}}
```
**Resultado**: Imagens organizadas em pares, lado a lado

## Como Usar no Template DOCX

### Opção 1: Layout Vertical (uma embaixo da outra)
Use a variável `items` normalmente:
```
{{#items}}
{{%image}}
Imagem {{numero}} – {{legenda}}

{{/items}}
```

### Opção 2: Layout Horizontal (lado a lado)
Use a variável `itemsPares` com uma tabela no Word:

1. **Crie uma tabela no Word** com 2 colunas e 1 linha
2. **Na primeira célula**, coloque:
```
{{#itemsPares}}
{{#imagem1}}
{{%image}}
Imagem {{numero}} – {{legenda}}
{{/imagem1}}
{{/itemsPares}}
```

3. **Na segunda célula**, coloque:
```
{{#itemsPares}}
{{#imagem2}}
{{%image}}
Imagem {{numero}} – {{legenda}}
{{/imagem2}}
{{/itemsPares}}
```

### Opção 3: Layout Misto
Você pode combinar ambas as abordagens no mesmo documento:

```
Imagens em destaque (lado a lado):
[Tabela com itemsPares]

Todas as imagens (lista completa):
{{#items}}
{{%image}}
Imagem {{numero}} – {{legenda}}
{{/items}}
```

## Estrutura dos Dados

### `items`
```javascript
[
  { image: "url1", numero: 1, legenda: "Descrição 1" },
  { image: "url2", numero: 2, legenda: "Descrição 2" },
  { image: "url3", numero: 3, legenda: "Descrição 3" },
  // ...
]
```

### `itemsPares`
```javascript
[
  {
    imagem1: { image: "url1", numero: 1, legenda: "Descrição 1" },
    imagem2: { image: "url2", numero: 2, legenda: "Descrição 2" }
  },
  {
    imagem1: { image: "url3", numero: 3, legenda: "Descrição 3" },
    imagem2: null // Se número ímpar de imagens
  }
]
```

## Dicas Importantes

1. **Para imagens lado a lado**: Sempre use uma tabela no Word para garantir o alinhamento
2. **Imagem ímpar**: Se houver número ímpar de imagens, a última ficará sozinha na segunda coluna
3. **Tamanho das imagens**: Atualmente configurado para 200x200 pixels
4. **Espaçamento**: Use quebras de linha no template para controlar o espaçamento entre as imagens

## Exemplo Prático

Para um relatório com 5 fotos, `itemsPares` criará:
- Par 1: Foto 1 + Foto 2
- Par 2: Foto 3 + Foto 4
- Par 3: Foto 5 + (vazio)

Isso permite criar layouts mais flexíveis e organizados nos seus relatórios.
